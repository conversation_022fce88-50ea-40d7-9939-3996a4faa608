import react from "@vitejs/plugin-react-swc";
import postcsspxtoviewport from "postcss-px-to-viewport";
import { defineConfig } from "vite";
import uploadResourcePlugin, { IServer } from "./plugins/uploadResourcePlugin";
import path from "path";
import reactPages from 'vite-plugin-react-pages';
import compresssionBuild from "rollup-plugin-compression";
import type { ICompressionOptions } from "rollup-plugin-compression";
import {  openAutoUploadDist,
  testServer,
  prodServer, isH5 } from "./app.json";

/**
 * @todo
 * 测试环境build自动上传服务器指定文件夹
 */
export default defineConfig(({ mode }) => {
  const serverInfo: IServer = {
    prodServer,
    testServer,
  };

  let targetName = "dist";

  if (mode?.includes("prod")) {
    targetName = prodServer.filePath || "dist";
  }

  const option: ICompressionOptions = {
    sourceName: `dist`,
    type: "zip",
    targetName,
    ignoreBase: true
  };

  return {
    plugins: [
      react(),
      reactPages(),
      openAutoUploadDist && uploadResourcePlugin(mode, serverInfo),
      mode?.includes("prod") && (compresssionBuild as any)?.default(option),
    ],
    resolve: {
      alias: {
        "@@": path.resolve(__dirname, "./"),
        "@": path.resolve(__dirname, "./src"),
        "@components": path.resolve(__dirname, "./src/components"),
        "@assets": path.resolve(__dirname, "./src/assets")
      },
    },
    minify: "terser",
    build: {
      outDir: "dist",
      chunkSizeWarningLimit: 1600,
    },
    css: {
      postcss: {
        plugins: isH5
          ? [
              postcsspxtoviewport({
                unitToConvert: "px", // 要转化的单位
                viewportWidth: 375, // UI设计稿的宽度
                unitPrecision: 6, // 转换后的精度，即小数点位数
                propList: ["*"], // 指定转换的css属性的单位，*代表全部css属性的单位都进行转换
                viewportUnit: "vw", // 指定需要转换成的视窗单位，默认vw
                fontViewportUnit: "vw", // 指定字体需要转换成的视窗单位，默认vw
                selectorBlackList: ["ignore-"], // 指定不转换为视窗单位的类名，
                minPixelValue: 1, // 默认值1，小于或等于1px则不进行转换
                mediaQuery: false, // 是否在媒体查询的css代码中也进行转换，默认false
                replace: true, // 是否转换后直接更换属性值
                // exclude: [/node_modules/], // 设置忽略文件，用正则做目录名匹配
                exclude: [],
                landscape: false, // 是否处理横屏情况
              }),
            ]
          : [],
      },
    },
  };
});
