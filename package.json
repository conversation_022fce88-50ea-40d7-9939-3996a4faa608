{"name": "nativatech", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build:test": "vite build --mode test", "build:prod": "vite build --mode prod", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@types/react-helmet": "6.1.9", "archiver": "6.0.1", "autoprefixer": "10.4.16", "bootstrap": "5.3.2", "clsx": "2.0.0", "device-detector-js": "3.0.3", "i18next": "23.7.6", "normalize.css": "8.0.1", "postcss": "8.4.31", "postcss-nested": "6.0.1", "react": "18.2.0", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "18.2.0", "react-helmet": "6.1.0", "react-i18next": "13.5.0", "react-markdown": "9.0.1", "react-native-localize": "3.0.3", "react-router-dom": "6.19.0", "react-vant": "3.3.2", "rollup-plugin-compression": "1.0.3", "ssh2-sftp-client": "9.1.0", "vconsole": "3.15.1", "vite-plugin-react-pages": "4.1.6", "vite-plugin-replace": "0.1.1"}, "devDependencies": {"@types/node": "^20.9.2", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "less": "^4.2.0", "less-loader": "^11.1.3", "postcss-px-to-viewport": "^1.1.1", "typescript": "^5.2.2", "vite": "^5.0.0"}}