import Client from "ssh2-sftp-client";
import fs from "fs";
import path from "path";
import sendFeishuMessage from "./sendMsgToFeishu";
const sftp = new Client();
const localDir = path.join(__dirname, "../dist");

/** 服务器配置信息 */
export interface IServer {
  prodServer: IServerInfo;
  testServer: IServerInfo;
}
/** 单台服务器配置信息 */
export interface IServerInfo {
  host: string;
  port: string;
  username: string;
  password: string;
  prefixPath: string;
  filePath: string;
}
/** 上传资源插件 */
function uploadResourcePlugin(mode: string, serverInfo: IServer) {
  return {
    name: "upload-resource-plugin",
    // bundle生成结束
    async closeBundle() {
      const { testServer, prodServer } = serverInfo;
      // 服务器配置获取
      let host = "",
        port = "",
        username = "",
        password = "";

      let outDir = "dist";

      if (mode?.includes("prod")) {
        outDir = prodServer.filePath || "dist";
      }
      if (mode?.includes("test")) {
        outDir = testServer.filePath || "dist";
      }

      /** 配置规则 */
      if (mode?.includes("test")) {
        host = testServer.host;
        port = testServer.port;
        username = testServer.username;
        password = testServer.password;
      } else if (mode?.includes("prod")) {
        host = prodServer.host;
        port = prodServer.port;
        username = prodServer.username;
        password = prodServer.password;
      }

      try {
        await sftp.connect({
          host,
          port,
          username,
          password,
        });
        const files = fs.readdirSync(localDir);
        /** 测试环境构建 */
        if (mode?.includes("test")) {
          for (const file of files) {
            const localFilePath = path.join(localDir, file);
            const remoteFilePath = `${testServer.prefixPath}${outDir}/` + file;
            try {
              const stats = fs.statSync(localFilePath);

              if (stats.isDirectory()) {
                // 确保远程目录存在
                try {
                  await sftp.mkdir(remoteFilePath, true);
                } catch (err) {
                  console.log(
                    `Directory ${remoteFilePath} already exists or creation failed`
                  );
                }

                // 使用 uploadDir 上传目录内容
                await sftp.uploadDir(localFilePath, remoteFilePath);
                await fs.rmdirSync(localFilePath, { recursive: true });
              } else {
                // 文件上传
                await sftp.put(localFilePath, remoteFilePath);
                await fs.unlinkSync(localFilePath);
              }

              console.log(`#upload ${localFilePath} => ${remoteFilePath} done`);
            } catch (err) {
              console.error(`Error processing ${localFilePath}:`, err);
              throw err;
            }
          }
        }

        /** 生产环境 */
        if (mode?.includes("prod")) {
          const directoryPath = path.join(__dirname, `../dist`);
          const outputPath = path.join(__dirname, `../${outDir}.zip`);
          const remoteFilePath =
            `${prodServer.prefixPath}${outDir}/` + `${outDir}.zip`;
          await sftp.put(outputPath, remoteFilePath);
          await fs.unlinkSync(outputPath);
          await fs.rmdirSync(directoryPath, { recursive: true });
          sendFeishuMessage({
            webhooks:
              "https://open.feishu.cn/open-apis/bot/v2/hook/67582f0a-a980-4d40-99bb-b314f91dcc4c",
            title: "【mexico two > 官网】",
            description: `上传生成环境文件路径成功, 使用jenkins进行build后可以进行验证!`,
            link: "https://auth.skyerapp.com//",
            linkText: "官网链接",
          });
        }
      } catch (error) {
        console.error("#upload error", error);
      } finally {
        await sftp.end();
        console.info("#upload done");
      }
    },
  };
}

export default uploadResourcePlugin;
