import Vconsole from "vconsole";
import DeviceDetector from "device-detector-js";

/** 获取用户基础信息 */
function getBaseInfo() {
  return {
    url: window.location.href,
    userAgent: navigator.userAgent,
  };
}

/** 获取用户设备信息 */
function getDeviceInfo() {
  const deviceDetector = new DeviceDetector();
  const device = deviceDetector.parse(navigator.userAgent);
  return device;
}

/** 获取用户定位信息 */
function getUserGeolocation() {
  return new Promise((resolve) => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const crd = position.coords;
        resolve({
          latitude: crd.latitude,
          longitude: crd.longitude,
          accuracy: crd.accuracy,
        });
      },
      (fail) => {
        console.log("获取用户定位信息失败:", fail); //获取失败的原因
      },
      {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 0,
      }
    );
  });
}

/** 获取用户剪贴板内容 */
function getClipboard() {
  return new Promise((resolve) => {
    navigator.clipboard
      .readText()
      .then(
        (clipText) => (resolve(clipText))
      );
  });
}

/** vConsole 测试反馈环境plugin */
const feedbackPlugin = new Vconsole.VConsolePlugin("tip_login", "反馈");

const html = `
   <div class="vc-item vc-item-info"><p>点击下方【复制...】按钮，复制信息发送给开发人员</p>
   <div class="vc-item vc-item-log"><p>页面链接：${getBaseInfo().url}</p></div>
   <div class="vc-item vc-item-log"><p>浏览器UA：${
     getBaseInfo().userAgent
   }</p></div>`;

const btnList: {
  name: string;
  global: boolean;
  onClick: (() => void) | (() => void) | (() => void);
}[] = [];
btnList.push({
  name: "复制用户信息",
  global: false,
  async onClick() {
    console.log("设备信息:", getDeviceInfo());
    console.log("定位信息:", await getUserGeolocation());
    console.log("剪贴板内容:", await getClipboard());
  },
});
// btnList.push({
//   name: "复制网络请求",
//   global: false,
//   onClick() {},
// });
// btnList.push({
//   name: "复制日志",
//   global: false,
//   onClick() {},
// });
// btnList.push({
//   name: "上报",
//   global: false,
//   onClick() {},
// });
feedbackPlugin.on("addTool", (callback) => {
  callback(btnList);
});

feedbackPlugin.on("renderTab", (callback) => {
  callback(html);
});

feedbackPlugin.on("init", function () {
  // do something
});

export default feedbackPlugin;
