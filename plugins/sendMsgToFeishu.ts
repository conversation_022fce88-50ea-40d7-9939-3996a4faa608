// 定义一个函数来发送飞书消息
interface IWebhooksPayload {
  webhooks: string;
  title: string;
  description: string;
  link: string;
  linkText: string;
}
async function sendFeishuMessage({
  webhooks,
  title,
  description,
  link,
  linkText,
}: IWebhooksPayload) {
  const api = webhooks;
  const payload = {
    msg_type: "post",
    content: {
      post: {
        zh_cn: {
          title: title,
          content: [
            [
              {
                tag: "text",
                text: description,
              },
            ],
            [],
            [
              {
                tag: "a",
                href: link,
                text: linkText,
              },
            ]
          ],
        },
      },
    },
  };

  try {
    const response = await fetch(api, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    console.log("消息发送成功");
  } catch (error) {
    console.error("发送消息失败:", error.message);
  }
}

export default sendFeishuMessage;
