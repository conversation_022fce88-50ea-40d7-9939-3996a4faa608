import navbarIcon from "@assets/home/<USER>";
import navbarLogoIcon from "@assets/home/<USER>";
import downloadIcon from "@assets/home/<USER>";
import { useEffect, useMemo, useState } from "react";
import { useLocation } from "react-router-dom";
import "./index.css";
import { googleStoreUrl } from "@@/app.json";
import { useNameSpace } from "@@/src/i18n";

const Navbar = () => {
  const location = useLocation();
  const { t } = useNameSpace("website");
  const [state, setState] = useState({
    navbarList: [
      {
        name: "Home",
        href: "/",
        isActive: true,
      },

      {
        name: "Aviso de Privacidad",
        href: "/privacypage",
        isActive: false,
      },
    ],
  });

  useEffect(() => {
    onSetCurNav(location.pathname);
    return () => {};
  }, [location]);

  const onSetCurNav = (pathname = "/") => {
    setState((preState) => {
      return {
        ...preState,
        navbarList: [
          {
            name: "Home",
            href: "/",
            isActive: pathname === "/",
          },

          {
            name: "Aviso de Privacidad",
            href: "/privacypage",
            isActive: pathname === "/privacypage",
          },
        ],
      };
    });
  };

  const { navbarList } = state;

  const $logo = useMemo(() => {
    return (
      <a className="navbar-brand" href="/">
        <img className="logo-style" src={navbarLogoIcon} alt="Logo" />
      </a>
    );
  }, []);

  const $navList = useMemo(() => {
    return (
      <>
        <button
          className="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNavAltMarkup"
          aria-controls="navbarNavAltMarkup"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <img className="toggle-icon-style" src={navbarIcon} />
        </button>
        <div
          className="collapse navbar-collapse navbar-collapse-style"
          id="navbarNavAltMarkup"
        >
          <div className="navbar-nav">
            {navbarList.map((item) => {
              return (
                <a
                  key={item.href}
                  className={`nav-link navabr-nav-item-style ${
                    item.isActive ? "active navabr-nav-item-active-style" : ""
                  }`}
                  aria-current="page"
                  href={item.href}
                >
                  {item.name}
                </a>
              );
            })}
            <a
              className="nav-link navabr-nav-item-style download-btn-style"
              href={googleStoreUrl}
            >
              <span style={{ color: "#fff", marginLeft: 24 }}>
                {t("download")}
              </span>
              <img
                src={downloadIcon}
                className="download-icon-style"
                alt="download"
              />
            </a>
          </div>
        </div>
      </>
    );
  }, [navbarList]);

  return (
    <>
      <nav className="navbar navbar-expand-lg bg-body-tertiary navbar-style">
        <div className="container">
          {$logo}
          {$navList}
        </div>
      </nav>
    </>
  );
};

export default Navbar;
