/** style **/
.navbar-style {
  background-color: var(--white) !important;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 9999;
  /* height: var(--navbar-height) */
}

.logo-style {
  width: 193px;
  height: 48px;
}

.toggle-icon-style {
  width: var(--toggle-icon-width);
  height: var(--toggle-icon-height);
}

.navbar-collapse-style {
  padding: 16px;
}

.navabr-nav-item-style {
  color: var(--text-color-600) !important;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  margin-left: 120px;
}

.navabr-nav-item-active-style {
  color: var(--text-color-800) !important;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 28px;
}

/** fix bootstrap **/
.navbar-collapse {
  flex-direction: row-reverse;
}

.download-btn-style {
  display: flex;
  align-items: center;
  border-radius: 999px;
  background: var(--primary-color-400, #8b77e8);
  text-decoration: none;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
}

.download-icon-style {
  width: 32px;
  height: 32px;
  margin-left: 10px;
  margin-right: 24px;
}
