import { companyEmail } from "@@/app.json";
import PrivacyBottomLock from "@assets/home/<USER>";
import PrivacyBottomBg from "@assets/home/<USER>";
import PrivacyBottomImg from "@assets/home/<USER>";
import CompanyIcon from "@assets/home/<USER>";
import EmailIcon from "@assets/home/<USER>";
import ContactIcon from "@assets/home/<USER>";
import "./index.css";
import { useNameSpace } from "@/i18n";

const BottomSection = () => {
  const t = useNameSpace("website").t;

  return (
    <>
      <div
        className="info-container"
        style={{
          backgroundImage: `url(${PrivacyBottomBg})`,
        }}
      >
        <div>
          <div className="privacy-bottom-title">{t("privacy")}</div>
          <div className="privacy-bottom-content">{t("privacy_tips")}</div>
        </div>

        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <img className="privacy-bottom-content-img" src={PrivacyBottomImg} />
          <img className="privacy-bottom-img" src={PrivacyBottomLock} />
        </div>
      </div>

      <div className="bottom-section-backround-style">
        <div className="container">
          <div className="bottom-section-container">
            <div className="bottom-item">
              <img className="bottom-item-icon" src={CompanyIcon} />
              <div className="bottom-item-note">{t("company")}</div>
            </div>
            <div className="bottom-item">
              <img className="bottom-item-icon" src={EmailIcon} />
              <div className="bottom-item-note bottom-item-note-link">
                {companyEmail}
              </div>
            </div>
            <div className="bottom-item">
              <img className="bottom-item-icon" src={ContactIcon} />
              <div className="bottom-item-note">{t("phone_service_tips")}</div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BottomSection;
