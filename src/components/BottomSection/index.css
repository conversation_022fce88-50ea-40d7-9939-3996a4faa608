.bottom-logo-style {
  width: 184px;
  height: 46px;
}

.app-icon-style {
  height: var(--icon-height);
  width: var(--icon-width);
}

.get-app-style {
  font-size: 18px;
}

.email-icon {
  width: 47px;
  height: 47px;
  margin-right: 12px;
}

.email-content {
  height: 40px;
  line-height: 40px;
  color: var(--white);
}

.company {
  color: var(--text-color-800);
  font-size: 20px;
  margin-bottom: 24px;
}

.label-item {
  flex: 1;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.label-name {
  margin-right: 48px;
  color: var(--text-color-800);
  font-size: 24px;
}

.label-text {
  color: var(--text-color-800);
  margin-top: 16px;
  font-size: 16px;
}

.bottom-item {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  color: var(--text-color-0, #fff);
  font-size: 20px;
  font-weight: 700;
  line-height: 28px;
}

.bottom-item-icon {
  width: 32px;
  height: 32px;
}

.bottom-item-note {
  color: var(--text-color-0, #fff);
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
  margin-left: 8px;
}

.bottom-item-note-link {
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
}

.info-container {
  background-color: rgba(139, 119, 232, 0.6);
  height: auto;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  /* padding-bottom: 50px; */
}

.privacy-bottom-title {
  color: #fff;
  font-size: 48px;
  font-weight: 600;
  line-height: normal;
  margin-left: 120px;
}

.privacy-bottom-content {
  color: var(--text-color-0, #fff);
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  margin-left: 120px;
  margin-top: 34px;
  margin-right: 200px;
}

.privacy-bottom-content-img {
  width: 220px;
  height: 176px;
  margin-right: 40px;
  align-self: center;
}

.privacy-bottom-img {
  width: 183px;
  height: 216px;
  top: 0;
  right: 0;
}

@media screen and (max-width: 1069px) {
  .bottom-section-backround-style {
    background-color: var(--black);
    height: auto;
    padding: 24px 16px;
    width: 100%;
  }
  .bottom-section-lable-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    margin-bottom: 24px;
  }

  .bottom-section-container {
    margin-top: 24px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-between;
  }

  .email-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }

  .bottom-line {
    display: none;
  }

  .bottom-item {
    margin: 0px 10px;
    margin-top: 32px;
  }
}

@media screen and (min-width: 1069px) {
  .bottom-section-backround-style {
    background-color: var(--black);
    height: auto;
    padding: 12px 8px;
    width: 100%;
  }

  .bottom-section-label-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 24px;
  }

  .bottom-section-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .email-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }

  .bottom-line {
    width: 1px;
    height: 46px;
    background-color: var(--white);
  }
}
