/**
 * 生成query参数
 * @param obj
 * @returns
 */
export function objectToQueryString(obj: {
  [x: string]: string | number | boolean;
}) {
  return Object.keys(obj)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
    .join("&");
}

/**
 * 生成随机字符串
 * @param characters
 * @param length
 * @returns
 */
export function generateRandomString(
  characters: string = "1234567890abcdefghijklmnopqrstuvwxyz",
  length: number = 16
) {
  let result = "";
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

/**
 * 随机生成uuid
 * @returns
 */
export function generateUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * 向webview发送消息
 * @param payload
 */
interface IPayload {
  uuid: string;
  action: "nextToTopRouter";
  sendTime?: Date;
}
export function sendWebviewMessage(payload: IPayload) {
  const _payload = {
    ...payload,
    sendTime: new Date(),
  };
  (window as any).ReactNativeWebView.postMessage(JSON.stringify(_payload));
}
