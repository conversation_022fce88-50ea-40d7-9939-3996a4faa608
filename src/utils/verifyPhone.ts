/**
 * 国家编码
 **/
enum ECountryCode {
  /** 墨西哥编码 */
  mexico = "MX",
  /** 秘鲁编码 */
  peru = "PE",
}
type countryCodeType = ECountryCode.mexico | ECountryCode.peru | "";
let counryCode: countryCodeType = "";
if (import.meta.env.MODE?.includes("mx")) {
  counryCode = ECountryCode.mexico;
} else if (import.meta.env.MODE?.includes("peru")) {
  counryCode = ECountryCode.peru;
}
/** 电话区号前缀 */
export const phoneNumberPrefix = () => {
  switch (counryCode) {
    case ECountryCode.mexico:
      return "+52";
    case ECountryCode.peru:
      return "+51";
  }
};

/** 电话区号前缀 */
export const phoneNumberPrefixNoplus = () => {
  switch (counryCode) {
    case ECountryCode.mexico:
      return "52";
    case ECountryCode.peru:
      return "51";
  }
};

/** 手机号格式校验 */
export const verifyPhoneNumber = (p: string = ""): boolean => {
  switch (counryCode) {
    case ECountryCode.mexico:
      return verrifyPhoneNumberForMx(p);
    case ECountryCode.peru:
      return verrifyPhoneNumberForPe(p);
    default:
      return verrifyPhoneNumberForPe(p);
  }
};

/** 秘鲁手机号校验规则 */
const verrifyPhoneNumberForPe = (p: string = ""): boolean => {
  if (typeof p !== "string") {
    return false;
  }

  let verifyResult = false;
  let formattedPhoneNumber = p.replace(/\s/g, "");
  if (formattedPhoneNumber.length > 9) {
    if (formattedPhoneNumber.startsWith(`+${phoneNumberPrefixNoplus()}`)) {
      formattedPhoneNumber = formattedPhoneNumber.replace(
        `+${phoneNumberPrefixNoplus()}`,
        ""
      );
    } else if (
      formattedPhoneNumber.startsWith(`${phoneNumberPrefixNoplus()}`)
    ) {
      formattedPhoneNumber = formattedPhoneNumber.replace(
        `${phoneNumberPrefixNoplus()}`,
        ""
      );
    } else if (
      formattedPhoneNumber.startsWith(`00${phoneNumberPrefixNoplus()}`)
    ) {
      formattedPhoneNumber = formattedPhoneNumber.replace(
        `00${phoneNumberPrefixNoplus()}`,
        ""
      );
    } else if (
      formattedPhoneNumber.startsWith(`+00${phoneNumberPrefixNoplus()}`)
    ) {
      formattedPhoneNumber = formattedPhoneNumber.replace(
        `+00${phoneNumberPrefixNoplus()}`,
        ""
      );
    }
    verifyResult = /^9\d{8}$/.test(formattedPhoneNumber);
  } else if (formattedPhoneNumber.length === 9) {
    verifyResult = /^9\d{8}$/.test(formattedPhoneNumber);
  } else {
    verifyResult = false;
  }
  return verifyResult;
};

/** 墨西哥手机号校验规则 */
const verrifyPhoneNumberForMx = (p: string = ""): boolean => {
  if (typeof p !== "string") {
    return false;
  }

  let verifyResult = false;
  let formattedPhoneNumber = p.replace(/\s/g, "");
  if (formattedPhoneNumber.length > 11) {
    if (formattedPhoneNumber.startsWith(`+${phoneNumberPrefixNoplus()}`)) {
      formattedPhoneNumber = formattedPhoneNumber.replace(
        `+${phoneNumberPrefixNoplus()}`,
        ""
      );
    } else if (
      formattedPhoneNumber.startsWith(`${phoneNumberPrefixNoplus()}`)
    ) {
      formattedPhoneNumber = formattedPhoneNumber.replace(
        `${phoneNumberPrefixNoplus()}`,
        ""
      );
    }
    verifyResult = /^(1[1-9]\d{9}|[1-9]\d{9})$/.test(formattedPhoneNumber);
  } else if (formattedPhoneNumber.length === 11) {
    verifyResult = /^1[1-9]\d{9}$/.test(formattedPhoneNumber);
  } else if (formattedPhoneNumber.length === 10) {
    verifyResult = /^[1-9]\d{9}$/.test(formattedPhoneNumber);
  } else {
    verifyResult = false;
  }
  return verifyResult;
};
