* {
  margin: 0;
  padding: 0;
}

:root {
  --brand-color: #1fcc9d;
  --white: rgba(255, 255, 255, 1);
  --grey: rgba(255, 255, 255, 0.6);
  --accent: rgba(249, 181, 1, 1);
  --neutral-300: rgba(209, 213, 219, 1);
  --neutral-900: rgba(17, 24, 39, 1);
  --border-color: rgba(166, 166, 166, 1);
  --black: rgba(0, 0, 0, 1);
  --radial: rgba(244, 250, 255, 1);
  --navbar-height: 80px;
  --logo-height: 36px;
  --logo-width: 176px;
  --toggle-icon-width: 32px;
  --toggle-icon-height: 32px;
  --icon-width: 16px;
  --icon-width: 16px;
  --gp-icon-height: 23px;
  --gp-icon-width: 25px;
  --gp-text-width: 85px;
  --gp-text-height: 27px;
  --app-home-first-height: 321px;
  --app-home-second-height: 270px;
  --app-home-third-height: 348px;
  --app-home-forth-height: 225px;
  --home-image-width: 327px;
  --home-image-two-width: 627px;
  --image-six-width: 142px;
  --back-icon-width: 24px;
  --back-icon-height: 24px;
  --blog-image-width: 360px;
  --text-color-600: rgba(0, 0, 0, 0.45);
  --text-color-800: rgba(0, 0, 0, 0.85);
  --primary-color-400: #8b77e8;
  --primary-color-500: #6b49ed;
  --neutral-color-900: rgba(17, 24, 39, 1);
  --background: rgba(247, 248, 251, 1);
}

.btn-default {
  border-radius: 5px;
  padding: 12px 24px;
  color: var(--white);
  background-color: var(--brand-color);
  border-color: var(--brand-color);
}

.btn-default:hover {
  border-radius: 5px;
  padding: 12px 24px;
  color: var(--white);
  background-color: var(--brand-color);
  border-color: var(--brand-color);
}

.btn-default:active {
  border-radius: 5px;
  padding: 12px 24px;
  color: var(--white);
  background-color: var(--brand-color);
  border-color: var(--brand-color);
}

.mt-8 {
  margin-top: 8px;
}

.mt-12 {
  margin-top: 12px;
}

.mt-48 {
  margin-top: 48px;
}

.mt-96 {
  margin-top: 96px;
}

.mb-48 {
  margin-bottom: 48px;
}

.ml-24 {
  margin-left: 24px;
}

.mr-24 {
  margin-right: 24px;
}

.m-16 {
  margin: 16px;
}

.p-16 {
  padding: 16px;
}

.pt-26 {
  padding-top: 26px;
}

.pb-26 {
  padding-bottom: 26px;
}

.pr-26 {
  padding-right: 26px;
}

.pb-40px {
  padding: 40px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

.pt-24 {
  padding-top: 24px;
}

.pb-24 {
  padding-bottom: 24px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-32 {
  margin-top: 32px;
}

.mt-64 {
  margin-top: 64px;
}

.mb-64 {
  margin-bottom: 64px;
}

.pt-64 {
  padding-top: 64px;
}

.pb-64 {
  padding-bottom: 64px;
}

.mr-16 {
  margin-right: 16px;
}

.mr-8 {
  margin-right: 8px;
}

.neutral-300 {
  color: var(--neutral-300);
}

.white {
  color: var(--white);
}

.block-center {
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  position: relative;
}

.bg-linear-gradient {
  background: linear-gradient(90deg, #4f52ff -9.13%, #00a0ff 88.66%);
}

.white-bg {
  background: var(--white);
}

.radial-bg {
  background: var(--radial);
}

.accent-bg {
  background: var(--accent);
}

.relative {
  position: relative;
}

/* 应用于屏幕宽度小于等于 1069px 的设备 */

@media screen and (max-width: 1069px) {
  .title {
    width: 100%;
    color: var(--neutral-900);
    font-size: 32px;
    line-height: 32px;
    font-weight: bold;
    padding-left: 16px;
    padding-right: 16px;
    position: relative;
  }
  .sub-title {
    width: 100%;
    color: var(--neutral-900);
    font-size: 24px;
    line-height: 24px;
    font-weight: bold;
    padding-left: 16px;
    padding-right: 16px;
    position: relative;
  }
  .content {
    color: var(--neutral-900);
    word-wrap: break-word;
    font-size: 18px;
    line-height: 28px;
    padding-left: 16px;
    padding-right: 16px;
    position: relative;
  }
}

/* 应用于屏幕宽度大于 1069px 的设备 */

@media screen and (min-width: 1069px) {
  .title {
    width: 100%;
    color: var(--neutral-900);
    font-size: 48px;
    line-height: 48px;
    font-weight: bold;
    padding-left: 16px;
    padding-right: 16px;
    text-align: left;
    position: relative;
  }
  .sub-title {
    width: 100%;
    color: var(--neutral-900);
    font-size: 24px;
    line-height: 24px;
    font-weight: bold;
    padding-left: 16px;
    padding-right: 16px;
    text-align: left;
    position: relative;
  }
  .content {
    color: var(--neutral-900);
    word-wrap: break-word;
    font-size: 18px;
    line-height: 28px;
    padding-left: 16px;
    padding-right: 16px;
    text-align: left;
    position: relative;
  }
  .center {
    text-align: center;
  }
}
