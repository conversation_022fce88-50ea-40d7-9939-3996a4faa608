type FetchOptions = RequestInit;
type FetchResponse = any;

interface IOptions {
  baseUrl: string;
}

class Api {
  baseUrl: string;

  SUCCESS_CODE = 0;
  ERROR_CODE = 500;

  constructor(options: IOptions) {
    const { baseUrl } = options;
    this.baseUrl = baseUrl;
  }

  get(url: string, options: FetchOptions = {}): Promise<FetchResponse> {
    return fetch(this.baseUrl + url, {
      headers: {
        "Content-Type": "application/json",
      },
      method: "GET",
      ...options,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .catch((error) => {
        console.error("Fetch error: ", error);
      });
  }

  post(
    url: string,
    body: any = {},
    options: FetchOptions = {}
  ): Promise<FetchResponse> {
    return fetch(this.baseUrl + url, {
      headers: {
        "Content-Type": "application/json",
      },
      method: "POST",
      body: JSON.stringify(body),
      ...options,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .catch((error) => {
        console.error("Fetch error: ", error);
      });
  }
}

export default Api;
