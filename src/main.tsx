import ReactDOM from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import Vconsole from 'vconsole';
import envPlugin from "../plugins/envPlugin"
import feedbackPlugin from "../plugins/feedbackPlugin"
import "bootstrap/dist/css/bootstrap.min.css";
import "normalize.css";
import "./index.css";
import App from "./App";

if (!import.meta.env.MODE?.includes('prod')) {
	const vConsole = new Vconsole()
	vConsole.addPlugin(feedbackPlugin);
	// vConsole.addPlugin(envPlugin);
}

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
	<>
		<BrowserRouter basename="/">
			<App />
		</BrowserRouter>
	</>
);
