import { useState, useEffect } from "react";
import { setup as setupForI18n } from "@/i18n";
import { openI18n, defaultLang } from "../../app.json";

const setup = () => {
  /** 初始化i18n */
  setupForI18n(openI18n ? navigator.language : defaultLang);
};

export default function useSetup() {
  let [isInited, setIsInited] = useState(false);

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    setup();
    setIsInited(true);
  };

  return { isInited };
}
