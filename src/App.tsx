import useSetup from "@/hooks/useSetup";
import { description, keywords, title } from "@@/app.json";
import { Helmet } from "react-helmet";
import { Route, Routes } from "react-router-dom";
import Home from "./pages/Home";
import PrivacyPage from "./pages/PrivacyPage";

function App() {
  const { isInited } = useSetup();

  if (!isInited) {
    return <></>;
  }
  return (
    <>
      <Helmet>
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="keywords" content={keywords} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Helmet>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/privacypage" element={<PrivacyPage />} />
        <Route path="*" element={<Home />} />
      </Routes>
    </>
  );
}

export default App;
