/**
 * Multi-language support
 *
 * @module i18n
 */
import { default as _i18n } from "i18next";
import { initReactI18next } from "react-i18next";
import { ITranslationModule } from "./type";

// hooks
export { useNameSpace } from "./useNameSpace";

// locals
import components from "./resources/components";
import website from "./resources/website";

const resources: {
  [key: string]: {
    translation: ITranslationModule;
  };
} = {
  es: {
    translation: {
      components: components.es,
      website: website.es,
    },
  },
  en: {
    translation: {
      components: components.en,
      website: website.en,
    },
  },
};

/**
 * Register business language module in sub business packages.
 * Please call this function before `setup`
 *
 * @param moduleName
 * @param translationModule
 */
export function registerTranslationModule(
  moduleName: string,
  translationModule: ITranslationModule
) {
  for (const language of Object.keys(translationModule)) {
    resources[language] = {
      translation: {
        ...resources[language].translation,
        [moduleName]: translationModule[language],
      },
    };
  }
}

export function setup(language: string) {
  _i18n.use(initReactI18next).init({
    lng: language || "es",
    fallbackLng: language || "es",
    resources,
    compatibilityJSON: "v3",
  });
}

export function changeLanguage(languageCode: string) {
  _i18n.changeLanguage(languageCode);
}

export const i18n = _i18n;
export const instance = _i18n;

export default {
  instance: _i18n,
  setup,
  changeLanguage,
  registerTranslationModule,
};
