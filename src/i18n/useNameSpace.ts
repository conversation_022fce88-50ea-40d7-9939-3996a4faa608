import { useTranslation } from 'react-i18next';

export const useNameSpace = (
  namespace: string = "",
  options?: { [key: string]: any },
) => {
  const { t, i18n } = useTranslation(undefined, options);
  const translateFunction = (key: string = "", ...params: any[]) => {
    let keyString = key
    if (namespace) keyString = `${namespace}.${key}`;
    if (!i18n.exists(keyString)) {
      return '';
    }
    return t(keyString as string, params as unknown as string);
  };

  return {
    t: translateFunction,
    i18n,
  };
};

export default useNameSpace;
