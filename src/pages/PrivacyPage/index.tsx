import Layout from "@@/src/components/Layout";
import { useNameSpace } from "@@/src/i18n";
import React, { useCallback, useEffect, useState } from "react";
import "./index.css";

const PrivacyPage: React.FC = () => {
  const { t } = useNameSpace("website");
  const resizeIframe = useCallback(() => {
    var iframe: any = document.querySelector("#myIframe");
    if (iframe) {
      iframe.style.height =
        iframe.contentWindow.document.body.scrollHeight + "px";
      // iframe.contentWindow.document.body.style.height = "100%";
      iframe.contentWindow.document.body.style.overflow = "hidden";
    }
  }, []);
  const $privacyView = (
    <>
      <div className="privacy-page-container">
        <div className="privacy-page-title">{t("privacy_policy")}</div>
        <iframe
          onLoad={resizeIframe}
          id="myIframe"
          src="/privacy/index.html"
          width="100%"
          style={{ marginBottom: 24 }}
        />
      </div>
    </>
  );
  return (
    <Layout>
      <>{$privacyView}</>
    </Layout>
  );
};

export default PrivacyPage;
