.app-home-first-style {
  height: var(--app-home-first-height);
  width: auto;
}

.app-home-second-style {
  height: var(--app-home-second-height);
  width: auto;
}

.app-home-third-style {
  height: var(--app-home-third-height);
  width: auto;
}

.accent-block-style {
  background: var(--accent);
  height: 142px;
}

.bg-circle-one-style {
  position: absolute;
  width: 1200px;
  height: 1200px;
  bottom: -1050px;
  border-radius: 50%;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
  background: linear-gradient(90deg, #4f52ff -9.13%, #00a0ff 88.66%);
}

.bg-circle-two-style {
  position: absolute;
  width: 320px;
  height: 320px;
  border-radius: 50%;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
  background: linear-gradient(90deg, #4f52ff -9.13%, #00a0ff 88.66%);
}


