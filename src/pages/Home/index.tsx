import { useCallback, useMemo } from "react";
import { useNameSpace } from "@/i18n";
import Layout from "@/components/Layout";
import GooglePlayButton from "@/components/GooglePlayButton";
import SectionOneBgWeb from "@assets/home/<USER>";
import SectionOneBg from "@assets/home/<USER>";
import SectionTwoImg from "@assets/home/<USER>";
import SectionTwoBg from "@assets/home/<USER>";
import SectionThreeImg from "@assets/home/<USER>";
import { googleStoreUrl } from "@@/app.json";

import "./index.css";
import "./web.css";
import "./mobile.css";

const Home = () => {
  const t = useNameSpace("website").t;
  const onGoGpStore = useCallback(() => {
    window.location.href = googleStoreUrl;
  }, []);

  const $sectionOne = useMemo(() => {
    return (
      <div
        className="section-one-bg"
        style={{
          backgroundImage: `url(${SectionOneBgWeb})`,
        }}
      >
        <div className="section-one-container container">
          <div className="section-one-item">
            <div className="section-one-title">
              {t("your_reliable_loan")} <br /> {t("safe_and_fast")}
            </div>
            <div className="section-one-note">{t("header_note")}</div>
            <div className="block-center mt-32">
              <GooglePlayButton />
            </div>
          </div>
          <div className="section-one-item" style={{ overflow: "hidden" }}>
            <img className="section-one-img" src={SectionOneBg} />
          </div>
        </div>
      </div>
    );
  }, []);

  const $sectionTwo = useMemo(() => {
    return (
      <div className="section-two-container">
        <img className="section-two-bg" src={SectionTwoBg} />
        <div className="section-two-item">
          <img className="section-two-img" src={SectionTwoImg} />
        </div>
        <div className="section-two-item">
          <div className="section-two-title">
            {t("offer_freedom_financial")}
          </div>
          <div className="section-two-content ">{t("oprate_describ")}</div>
        </div>
      </div>
    );
  }, []);

  const $sectionThree = useMemo(() => {
    const contentList = [
      t("how_to_use_content1"),
      t("how_to_use_content2"),
      t("how_to_use_content3"),
      t("how_to_use_content4"),
    ];
    return (
      <div className="section-three-container">
        <div className="section-three-item">
          <div className="section-three-title">{t("how_to_use")}</div>
          {contentList.map((content, index) => (
            <div
              className="section-three-content"
              key={content}
              style={{ marginTop: index === 0 ? 0 : 32 }}
            >
              {content}
            </div>
          ))}
        </div>
        <div className="section-three-item">
          <img className="section-three-img" src={SectionThreeImg} />
        </div>
      </div>
    );
  }, []);

  return (
    <Layout>
      <>
        {$sectionOne}
        {$sectionTwo}
        {$sectionThree}
      </>
    </Layout>
  );
};

export default Home;
