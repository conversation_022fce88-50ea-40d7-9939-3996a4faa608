/* 应用于屏幕宽度小于等于 1069px 的设备 */
@media screen and (max-width: 1069px) {
  .section-one-bg {
    background-color: var(--primary-color-500);
  }

  .section-one-container {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    align-items: center;
  }

  .section-one-title {
    color: var(--white);
    font-size: 24px;
    line-height: 29px;
    font-weight: 800;
  }

  .section-one-note {
    color: var(--white);
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }

  .section-one-img {
    width: 70%;
    height: auto;
    margin: auto;
  }

  .section-one-item {
    margin-top: 32px;
  }

  .section-one-item:nth-child(1) {
    display: flex;
    align-items: center;
    flex-direction: column;
  }

  .section-one-item:nth-child(2) {
    display: flex;
    align-items: center;
  }

  .section-three-main-bg {
    background-color: var(--background);
  }

  .section-three-main {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding-top: 64px;
  }

  .section-three-main > img {
    display: none;
  }

  .section-three-main-title {
    font-weight: 500;
    font-size: 20px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    margin-left: 48px;
    margin-right: 48px;
    white-space: nowrap;
  }

  .section-three-main-container {
    padding-top: 32px;
    padding-bottom: 64px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-between;
  }

  .section-three-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .section-three-item:nth-child(1) {
    margin-top: 36px;
  }
  .section-three-item:nth-child(n + 1) {
    margin-top: 60px;
  }
  .section-three-item-img {
    padding-top: 24px;
    padding-bottom: 10px;
  }
  .section-three-item-right-trigle > img {
    display: none;
  }
  .section-three-item-img > img {
    width: 180px;
    height: 180px;
  }
  .section-three-item-desc {
    font-weight: 700;
    font-size: 15px;
    line-height: 28px;
    text-align: center;
  }

  .section-six-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    padding: 32px 0;
  }
  .section-six-item {
  }
  .section-six-item:nth-child(2) {
    margin-top: 24px;
  }
  .section-six-title {
    font-size: 24px;
    line-height: 29px;
    font-weight: 700;
    color: var(--neutral-color-900);
    text-align: center;
  }
  .section-six-content {
    margin-top: 24px;
    font-weight: 400;
    font-size: 18px;
    line-height: 28px;
    text-align: center;
  }
  .section-six-img {
    width: 256px;
    height: 256px;
  }

  .section-eight-bg {
    background-color: var(--primary-color-500);
  }
  .section-eight-container {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top: 24px;
  }
  .section-eight-content-container {
  }
  .section-eight-content-item {
  }

  .section-eight-container > img {
    display: none;
  }

  .section-eight-title {
    font-size: 48px;
    line-height: 58px;
    font-weight: 600;
    color: var(--white);
    text-align: center;
    margin-left: 48px;
    margin-right: 48px;
    white-space: nowrap;
  }

  .section-nine-container {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column-reverse;
    z-index: 10;
  }

  .section-nine-bg {
    position: relative;
  }
  .wave {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 1;
  }
  .section-nine-item {
  }
  .section-nine-title {
    font-size: 48px;
    line-height: 58px;
    font-weight: 600;
    margin-top: 24px;
    color: var(--neutral-color-900);
    text-align: center;
  }
  .section-nine-content {
    margin-top: 24px;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
  }

  .app-home-forth-style {
    width: 50%;
    height: auto;
    z-index: 10;
  }

  .box {
  }

  .box-reverse {
  }

  .box-2 {
    width: 100%;
  }

  .box-3 {
  }

  .box-space-half {
    margin-top: 32px;
  }

  .box-space {
    padding-top: 64px;
  }

  .home-image-style {
    width: 300px;
    height: auto;
  }

  .card-lg-style {
    width: 100%;
    position: relative;
  }

  .privacidada-box-lg-style {
    text-align: center;
  }

  .card-person-image-style {
    display: none;
  }

  .card-style {
    box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.1);
    padding: 16px;
    padding-left: 32px;
    border-radius: 10px;
    position: relative;
  }

  .card-image-top-style {
    top: 0px;
  }

  .card-shadow {
    box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.1);
  }

  .symbol-one-style {
    width: 16px;
    height: 13px;
    position: absolute;
    left: 18px;
    top: 18px;
  }

  .symbol-two-style {
    width: 142px;
    height: 116px;
    position: absolute;
    right: 12px;
    top: -100px;
    display: none;
  }

  .symbol-third-style {
    width: 142px;
    height: 116px;
    position: absolute;
    left: 0;
    top: -32px;
  }

  .image-six-style {
    display: none;
  }

  .no-cobramos-block-style {
    margin-top: 48px;
  }

  .consulta-tus {
    margin-left: 0;
  }

  .home-image-two-style {
    width: 340px;
    height: auto;
  }

  .bg-trangle-one-style {
    display: none;
  }
}

@media screen and (max-width: 1240px) {
  .section-two-container {
    margin-top: 48px;
    margin-bottom: 0;
    display: flex;
    flex-direction: column-reverse;
    justify-content: space-between;
    align-items: center;
  }

  .section-two-img {
    max-width: 500px;
    width: 100%;
    height: auto;
  }

  .section-two-item {
    width: 100%;
  }

  .section-two-item:nth-child(1) {
    margin-top: 48px;
    display: flex;
    justify-content: space-around;
  }

  .section-two-title {
    font-size: 32px;
    font-style: normal;
    font-weight: 900;
    line-height: 42px;
    color: #6b49ed;
  }
  .section-two-content {
    margin-top: 24px;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    color: #8b77e8;
    text-align: center;
  }
  .section-two-button {
    margin-top: 60px;
    display: flex;
  }

  .section-two-button > .btn {
    margin: auto;
  }

  .section-two-item {
  }
}
