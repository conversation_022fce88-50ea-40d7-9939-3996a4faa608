/* 应用于屏幕宽度大于 1069px 的设备 */
@media screen and (min-width: 1069px) {
  .section-one-bg {
    background-color: var(--primary-color-500);
  }

  .section-one-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .section-one-title {
    color: var(--white);
    font-size: 48px;
    line-height: 58px;
    font-weight: 800;
  }

  .section-one-note {
    color: var(--white);
    font-size: 32px;
    font-weight: 600;
    line-height: 48px;
  }

  .section-one-img {
    width: 546px;
    height: auto;
    margin: auto;
  }

  .section-one-item {
    width: 50%;
  }
  /* 
  .section-two-container {
    background: linear-gradient(
        180deg,
        rgba(245, 244, 250, 0.6) 0%,
        rgba(207, 207, 255, 0.6) 100%
      ),
      #f5f4fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
  } */
  .section-three-container {
    /* margin-top: 60px; */
    /* margin-bottom: 80px; */
    /* flex-direction: row; */
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(
        180deg,
        rgba(245, 244, 250, 0.6) 0%,
        rgba(207, 207, 255, 0.6) 100%
      ),
      #f5f4fa;
    height: 620px;
    padding-left: 120px;
    padding-right: 120px;
  }

  .section-three-img {
    width: 457px;
    height: 571px;
    margin-right: 120px;
  }

  .section-three-item {
    width: 50%;
  }

  .section-three-title {
    color: var(--primary-color-500);
    font-size: 48px;
    font-style: normal;
    font-weight: 700;
    line-height: 60px;
    margin-bottom: 48px;
  }
  .section-three-content {
    color: var(--text-color-800);
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
  }

  .section-three-main-bg {
    background-color: var(--background);
  }

  .section-three-main {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding-top: 64px;
  }

  .section-three-main-title {
    font-weight: 600;
    font-size: 48px;
    height: 48px;
    line-height: 48px;
    text-align: center;
    margin-left: 48px;
    margin-right: 48px;
    white-space: nowrap;
  }

  .section-three-main-container {
    padding-top: 32px;
    padding-bottom: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .section-three-item {
    display: flex;
    flex-direction: column;
  }
  .section-three-item-img {
    padding-top: 24px;
    padding-bottom: 10px;
  }
  .section-three-item-right-trigle > img {
    width: 34px;
    height: 63px;
  }
  .section-three-item-img > img {
    width: 180px;
    height: 180px;
  }
  .section-three-item-desc {
    font-weight: 700;
    font-size: 24px;
    line-height: 29px;
    font-weight: 700;
    text-align: center;
  }

  .section-six-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 80px 0;
  }
  .section-six-item {
  }
  .section-six-title {
    font-size: 48px;
    line-height: 58px;
    font-weight: 600;
    color: var(--neutral-color-900);
  }
  .section-six-content {
    margin-top: 24px;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }
  .section-six-img {
    width: 256px;
    height: 256px;
  }

  .section-eight-bg {
    background-color: var(--primary-color-500);
  }
  .section-eight-container {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top: 64px;
  }
  .section-eight-content-container {
    margin-top: 64px;
    margin-bottom: 64px;
  }
  .section-eight-content-item {
  }

  .section-eight-title {
    font-size: 48px;
    line-height: 58px;
    font-weight: 600;
    color: var(--white);
    text-align: center;
    margin-left: 48px;
    margin-right: 48px;
    white-space: nowrap;
  }

  .section-nine-container {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10;
  }
  .section-nine-bg {
    position: relative;
  }
  .wave {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 1;
  }
  .section-nine-item {
  }
  .section-nine-title {
    font-size: 48px;
    line-height: 58px;
    font-weight: 600;
    color: var(--neutral-color-900);
    text-align: right;
  }
  .section-nine-content {
    margin-top: 24px;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    text-align: right;
  }

  .app-home-forth-style {
    width: 418px;
    height: 550px;
    z-index: 10;
  }

  .box {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .box-reverse {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .box-2 {
    width: 50%;
  }

  .box-3 {
    width: 30%;
  }

  .box-space-half {
    margin-top: 0;
  }

  .box-space {
    padding-top: 26px;
  }

  .home-image-style {
    width: 327px;
    height: auto;
  }

  .privacidada-box-lg-style {
    text-align: right;
  }

  .card-person-image-style {
    position: absolute;
    width: 210px;
    height: 385px;
    left: -210px;
    bottom: 0;
  }

  .card-style {
    box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.1);
    min-height: 468px;
    padding: 16px;
    padding-left: 32px;
    border-radius: 10px;
    position: relative;
  }

  .card-image-top-style {
    top: 40px;
  }

  .card-shadow {
    box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.1);
  }

  .symbol-one-style {
    width: 16px;
    height: 13px;
    position: absolute;
    left: 18px;
    top: 18px;
  }

  .symbol-two-style {
    width: 142px;
    height: 116px;
    position: absolute;
    right: 12px;
    top: -66px;
  }

  .symbol-third-style {
    width: 142px;
    height: 116px;
    position: absolute;
    left: 0;
    top: -32px;
  }

  .image-six-style {
    position: absolute;
    width: 189px;
    height: 377px;
    height: auto;
    bottom: 0;
    right: -180px;
  }

  .home-image-two-style {
    width: 627px;
    height: auto;
  }

  .consulta-tus {
    margin-left: 148px;
  }

  .no-cobramos-block-style {
    margin-top: 0px;
  }

  .bg-trangle-one-style {
    position: absolute;
    width: 66%;
    height: 100%;
    border-top: 0;
    border-right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    background: linear-gradient(90deg, #4f52ff -9.13%, #00a0ff 88.66%);
    -webkit-clip-path: polygon(0 0, 0% 100%, 100% 100%);
    clip-path: polygon(0 0, 0% 100%, 100% 100%);
  }
}

@media screen and (min-width: 1240px) {
  .section-two-container {
    /* margin-top: 60px; */
    /* margin-bottom: 80px; */
    /* flex-direction: row; */
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(
        180deg,
        rgba(245, 244, 250, 0.6) 0%,
        rgba(207, 207, 255, 0.6) 100%
      ),
      #f5f4fa;
    height: 600px;
  }

  .section-two-bg {
    width: 960px;
    height: 600px;
    position: absolute;
  }

  .section-two-img {
    width: 500px;
    height: 500px;
    margin-left: 120px;
  }

  .section-two-item {
    width: 50%;
  }

  .section-two-title {
    font-size: 64px;
    font-style: normal;
    font-weight: 900;
    line-height: 84px;
    color: #6b49ed;
  }
  .section-two-content {
    margin-top: 48px;
    font-size: 32px;
    font-weight: 700;
    line-height: 48px;
    color: #8b77e8;
  }
  .section-two-button {
    margin-top: 60px;
  }
}
